import os
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import glob

# Set font to Times New Roman for IEEE journal requirements
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 12

# Configuration
INPUT_FOLDER = 'snr_results_tables'
OUTPUT_FOLDER = 'snr_plots'

# Plot styling configuration - Top journal color scheme for clear distinction
PLOT_STYLES = {
    'CLDNN': {'marker': 'D', 'linestyle': ':', 'color': '#1f77b4'},      # Blue
    'AWN': {'marker': 's', 'linestyle': '--', 'color': '#ff7f0e'},       # Orange
    'MAMC': {'marker': '<', 'linestyle': '--', 'color': '#2ca02c'},      # Green
    'MAWDN': {'marker': '>', 'linestyle': '-.', 'color': '#9467bd'},     # Purple
    'WNN-MRNN': {'marker': 'o', 'linestyle': '-', 'color': '#d62728'}    # Red
}

def create_output_folder():
    """Create output folder for plots"""
    if not os.path.exists(OUTPUT_FOLDER):
        os.makedirs(OUTPUT_FOLDER)
        print(f"Created output folder: {OUTPUT_FOLDER}")
    else:
        print(f"Output folder already exists: {OUTPUT_FOLDER}")

def get_available_files():
    """Get list of available Excel files"""
    pattern = os.path.join(INPUT_FOLDER, "snr_results_*.xlsx")
    files = glob.glob(pattern)
    return [os.path.basename(f) for f in files]

def extract_dataset_name(filename):
    """Extract dataset name from filename"""
    # Remove 'snr_results_' prefix and '.xlsx' suffix
    return filename.replace('snr_results_', '').replace('.xlsx', '')

def parse_snr_value(snr_str):
    """Parse SNR string to numeric value"""
    try:
        # Remove 'dB' suffix and convert to float
        return float(snr_str.replace('dB', ''))
    except:
        return None

def plot_single_dataset(excel_file, selected_models=None):
    """Plot SNR vs Accuracy for a single dataset"""

    # Read Excel file
    file_path = os.path.join(INPUT_FOLDER, excel_file)
    df = pd.read_excel(file_path)

    # Extract dataset name
    dataset_name = extract_dataset_name(excel_file)

    # Get SNR columns (all columns except 'Model Name')
    snr_columns = [col for col in df.columns if col != 'Model Name']

    # Parse SNR values
    snr_values = []
    for col in snr_columns:
        snr_val = parse_snr_value(col)
        if snr_val is not None:
            snr_values.append(snr_val)

    if not snr_values:
        print(f"Warning: No valid SNR values found in {excel_file}")
        return

    # Sort SNR values
    snr_indices = np.argsort(snr_values)
    sorted_snr_values = [snr_values[i] for i in snr_indices]
    sorted_snr_columns = [snr_columns[i] for i in snr_indices]

    # Create plot with IEEE journal formatting (4:3 ratio)
    plt.figure(figsize=(8, 6))
    
    # Plot each model
    for _, row in df.iterrows():
        model_name = row['Model Name']
        
        # Skip if model not in selected models (when specified)
        if selected_models and model_name not in selected_models:
            continue
        
        # Extract accuracy values
        accuracies = []
        valid_snr_values = []
        
        for snr_col, snr_val in zip(sorted_snr_columns, sorted_snr_values):
            acc_val = row[snr_col]
            if pd.notna(acc_val) and acc_val != '':
                try:
                    # Convert percentage to decimal (0-1 range)
                    acc_decimal = float(acc_val) / 100.0
                    accuracies.append(acc_decimal)
                    valid_snr_values.append(snr_val)
                except:
                    continue
        
        if not accuracies:
            print(f"Warning: No valid accuracy data for model {model_name}")
            continue
        
        # Get plot style
        style = PLOT_STYLES.get(model_name, {'marker': 'o', 'linestyle': '-', 'color': 'black'})
        
        # Plot line
        plt.plot(valid_snr_values, accuracies,
                marker=style['marker'],
                linestyle=style['linestyle'],
                color=style['color'],
                label=model_name,
                linewidth=2,
                markersize=6)

    # Customize plot for IEEE journal requirements
    plt.xlabel('SNR (dB)', fontsize=12, fontfamily='Times New Roman')
    plt.ylabel('Accuracy', fontsize=12, fontfamily='Times New Roman')
    # Remove title as requested
    plt.grid(True, alpha=0.3)
    # Place legend in upper left corner with larger font
    plt.legend(loc='upper left', fontsize=12, frameon=True, fancybox=False, shadow=False)

    # Set y-axis limits and format (0.1, 0.2 format instead of percentage)
    plt.ylim(0, 1.05)
    plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1f}'.format(y)))

    # Set tick parameters for IEEE journal style
    plt.tick_params(axis='both', which='major', labelsize=10)

    # Create inset axes for zoomed region (5-20 dB)
    # Position: [x0, y0, width, height] in axes coordinates
    axins = plt.gca().inset_axes([0.15, 0.6, 0.35, 0.35])

    # Plot the same data in the inset axes
    for _, row in df.iterrows():
        model_name = row['Model Name']

        # Skip if model not in selected models (when specified)
        if selected_models and model_name not in selected_models:
            continue

        # Extract accuracy values for zoom region
        zoom_accuracies = []
        zoom_snr_values = []

        for snr_col, snr_val in zip(sorted_snr_columns, sorted_snr_values):
            # Only include SNR values in the zoom range (5-20 dB)
            if 5 <= snr_val <= 20:
                acc_val = row[snr_col]
                if pd.notna(acc_val) and acc_val != '':
                    try:
                        acc_decimal = float(acc_val) / 100.0
                        zoom_accuracies.append(acc_decimal)
                        zoom_snr_values.append(snr_val)
                    except:
                        continue

        if zoom_accuracies:
            # Get plot style
            style = PLOT_STYLES.get(model_name, {'marker': 'o', 'linestyle': '-', 'color': 'black'})

            # Plot in inset axes
            axins.plot(zoom_snr_values, zoom_accuracies,
                      marker=style['marker'],
                      linestyle=style['linestyle'],
                      color=style['color'],
                      linewidth=2,
                      markersize=4)

    # Set zoom region limits
    axins.set_xlim(5, 20)
    # Auto-adjust y limits for the zoom region
    axins.set_ylim(auto=True)

    # Format inset axes
    axins.grid(True, alpha=0.3)
    axins.tick_params(axis='both', which='major', labelsize=8)
    axins.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1f}'.format(y)))

    # Add rectangle and connecting lines to show zoom region
    plt.gca().indicate_inset_zoom(axins, edgecolor='black', linewidth=1.5, alpha=0.8)

    # Tight layout
    plt.tight_layout()
    
    # Save plot
    output_filename = f"snr_accuracy_{dataset_name}.png"
    output_path = os.path.join(OUTPUT_FOLDER, output_filename)
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Saved plot: {output_path}")

    # Close the plot to free memory
    plt.close()

# Interactive functions removed - focusing on RML dataset only

def main():
    """Main function - Focus on RML dataset only with all models"""
    print("=" * 60)
    print("SNR Results Plotting Tool - RML Dataset")
    print("=" * 60)

    # Create output folder
    create_output_folder()

    # Look for RML dataset file specifically
    rml_file = "snr_results_rml.xlsx"
    rml_path = os.path.join(INPUT_FOLDER, rml_file)

    if not os.path.exists(rml_path):
        print(f"Error: RML dataset file not found: {rml_path}")
        print("Please ensure the file 'snr_results_rml.xlsx' exists in the input folder.")
        return

    print(f"Processing RML dataset with all models...")

    # Create plot for RML dataset with all models
    plot_single_dataset(rml_file, selected_models=None)

    print(f"\n=" * 60)
    print(f"RML plot saved to: {OUTPUT_FOLDER}")
    print("=" * 60)

if __name__ == '__main__':
    main()
